<template>
  <view class="fruit-detail-container" v-if="fruitDetail">
    <!-- 标题 -->
    <text class="detail-title">果树详情</text>
    
    <!-- 富文本内容 -->
    <view class="rich-text-wrapper">
      <rich-text
        :nodes="processedFruitDetail"
        class="rich-text-content"
      ></rich-text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  fruitDetail: {
    type: String,
    default: ''
  }
})

// 处理富文本内容，给图片添加自适应样式
const processedFruitDetail = computed(() => {
  if (!props.fruitDetail) return ''

  // 使用正则表达式替换img标签，添加自适应样式
  return props.fruitDetail.replace(
    /<img([^>]*?)>/gi,
    (match, attributes) => {
      // 检查是否已经有style属性
      if (attributes.includes('style=')) {
        // 如果已有style属性，在其中添加宽度和高度样式
        return match.replace(
          /style=["']([^"']*?)["']/gi,
          (_, styleContent) => {
            const newStyle = styleContent + ';width:100%;height:auto;'
            return `style="${newStyle}"`
          }
        )
      } else {
        // 如果没有style属性，直接添加
        return `<img${attributes} style="width:100%;height:auto;">`
      }
    }
  )
})
</script>

<style lang="scss" scoped>
// 设计变量
$white-color: #ffffff;
$text-dark: #1a1a1a;
$text-gray: #666666;
$border-color: #e5e5e5;

.fruit-detail-container {
  background-color: $white-color;
  margin: 20rpx 0;
  padding: 40rpx;
  margin-bottom: 120rpx; // 为底部操作栏留出空间
}

.detail-title {
  display: block;
  text-align: center;
  font-size: 32rpx;
  font-weight: 700;
  line-height: 55rpx;
  color: $text-dark;
  margin-bottom: 30rpx;
}

.rich-text-wrapper {
  width: 100%;
  overflow: hidden;
}

.rich-text-content {
  width: 100%;
  line-height: 1.6;
  overflow: hidden;
  
  // 富文本内容样式优化
  :deep(p) {
    margin: 16rpx 0;
    font-size: 28rpx;
    line-height: 1.6;
    color: $text-dark;
  }
  
  :deep(img) {
    max-width: 100% !important;
    width: 100% !important;
    height: auto !important;
    display: block;
    margin: 20rpx auto;
    border-radius: 8rpx;
    box-sizing: border-box;
  }
  
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    font-weight: 600;
    margin: 24rpx 0 16rpx 0;
    color: $text-dark;
  }
  
  :deep(h1) { font-size: 36rpx; }
  :deep(h2) { font-size: 34rpx; }
  :deep(h3) { font-size: 32rpx; }
  :deep(h4) { font-size: 30rpx; }
  :deep(h5) { font-size: 28rpx; }
  :deep(h6) { font-size: 26rpx; }
  
  :deep(ul), :deep(ol) {
    margin: 16rpx 0;
    padding-left: 40rpx;
  }
  
  :deep(li) {
    margin: 8rpx 0;
    font-size: 28rpx;
    line-height: 1.6;
    color: $text-dark;
  }
  
  :deep(blockquote) {
    margin: 20rpx 0;
    padding: 20rpx;
    background-color: #f8f8f8;
    border-left: 8rpx solid #ddd;
    border-radius: 8rpx;
  }
  
  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 20rpx 0;
  }
  
  :deep(th), :deep(td) {
    border: 1rpx solid $border-color;
    padding: 16rpx;
    text-align: left;
    font-size: 26rpx;
  }
  
  :deep(th) {
    background-color: #f8f8f8;
    font-weight: 600;
  }
  
  :deep(a) {
    color: #007aff;
    text-decoration: underline;
  }
  
  :deep(strong), :deep(b) {
    font-weight: 600;
  }
  
  :deep(em), :deep(i) {
    font-style: italic;
  }
  
  :deep(code) {
    background-color: #f1f1f1;
    padding: 4rpx 8rpx;
    border-radius: 4rpx;
    font-family: monospace;
    font-size: 24rpx;
  }
  
  :deep(pre) {
    background-color: #f8f8f8;
    padding: 20rpx;
    border-radius: 8rpx;
    overflow-x: auto;
    margin: 20rpx 0;
    
    code {
      background-color: transparent;
      padding: 0;
    }
  }
}
</style>
