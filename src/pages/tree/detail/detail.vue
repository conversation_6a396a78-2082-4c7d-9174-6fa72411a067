<template>
  <view class="detail-container">
    <!-- 轮播图组件 -->
    <DetailSwiper :images="treeDetail.imageUrls || []" />

    <!-- 产品信息组件 -->
    <ProductInfo
      :name="treeDetail.name"
      :description="treeDetail.description"
      :price="treeDetail.price"
      :salesCount="treeDetail.salesCount"
    />

    <!-- 产品权益组件 -->
    <ProductBenefits :benefits="treeDetail.benefits" />
    
    <!-- 商品详情组件 -->
    <ProductDetails :details="treeDetail" />

    <!-- 果树详情富文本组件 -->
    <FruitDetail :fruitDetail="treeDetail.fruitDetail" />


    <!-- 底部操作栏组件 -->
    <BottomActions
      :productInfo="treeDetail"
    />
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { getFruitTreeDetail } from '@/api/tree'
import DetailSwiper from './components/DetailSwiper.vue'
import ProductInfo from './components/ProductInfo.vue'
import ProductBenefits from './components/ProductBenefits.vue'
import FruitDetail from './components/FruitDetail.vue'
import ProductDetails from './components/ProductDetails.vue'
import BottomActions from './components/BottomActions.vue'

// 响应式数据
const treeId = ref('')
const treeDetail = ref({
  imageUrls: [],
  freightPrice: 0
})

// 页面加载时获取参数
onLoad((options) => {
  if (options.id) {
    treeId.value = options.id
    loadTreeDetail()
  }
})

// 加载果树详情数据
const loadTreeDetail = async () => {
  try {
    const res = await getFruitTreeDetail(treeId.value)
    treeDetail.value = res.data
    console.log(treeDetail.value, 'treeDetail')
  } catch (error) {
    console.error('加载果树详情失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 页面初始化
onMounted(() => {
  console.log('果树详情页面已加载')
})

// 分享给朋友
onShareAppMessage(() => {
  return {
    title: treeDetail.value.name,
    path: `/pages/tree/detail/detail?id=${treeId.value}`,
    imageUrl: treeDetail.value.imageUrls[0]
  }
})

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: treeDetail.value.name,
    query: `id=${treeId.value}`,
    imageUrl: treeDetail.value.imageUrls[0]
  }
})
</script>

<style lang="scss" scoped>
// 设计变量
$primary-color: #dd3c29;
$white-color: #ffffff;
$text-gray: #666666;
$text-dark: #1a1a1a;
$bg-gray: #f5f5f5;

// 混入
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 页面容器
.detail-container {
  min-height: 100vh;
  background-color: $bg-gray;
  display: flex;
  flex-direction: column;
}
</style>
